package com.ebon.energy.fms.common.utils;

public final class AddressUtility {

    private AddressUtility() {
    }

    public static String compressStreetComponents(String streetNumber, String streetName) {
        String s1 = streetNumber == null ? "" : streetNumber;
        String s2 = streetName == null ? "" : streetName;
        return (s1 + " " + s2).trim();
    }

    public static String formattedAddress(IRedbackAddress redbackAddress) {
        StringBuilder address = new StringBuilder();

        if (!isNullOrEmpty(redbackAddress.getAddressLineOne())) {
            address.append(redbackAddress.getAddressLineOne()).append(" ");
        }
        if (!isNullOrEmpty(redbackAddress.getAddressLineTwo())) {
            address.append(redbackAddress.getAddressLineTwo()).append(" ");
        }
        if (!isNullOrEmpty(redbackAddress.getSuburb())) {
            address.append(redbackAddress.getSuburb()).append(", ");
        }
        if (!isNullOrEmpty(redbackAddress.getState())) {
            address.append(redbackAddress.getState()).append(", ");
        }
        if (!isNullOrEmpty(redbackAddress.getPostCode())) {
            address.append(redbackAddress.getPostCode()).append(", ");
        }
        // C# StringBuilder.Append(null) 不追加任何字符；在 Java 中避免追加 "null"
        String country = redbackAddress.getCountry();
        if (country != null) {
            address.append(country);
        }

        return address.toString();
    }

    /**
     * Breaks the Suburb, State, Postcode so that it can be shown as bold in any
 list.
     */
    public static String localityString(IRedbackAddress redbackAddress) {
        StringBuilder address = new StringBuilder();

        if (!isNullOrEmpty(redbackAddress.getSuburb())) {
            address.append(redbackAddress.getSuburb()).append(", ");
        }
        if (!isNullOrEmpty(redbackAddress.getState())) {
            address.append(redbackAddress.getState()).append(", ");
        }
        String postCode = redbackAddress.getPostCode();
        if (postCode != null) {
            address.append(postCode);
        }

        return address.toString();
    }

    private static boolean isNullOrEmpty(String s) {
        return s == null || s.isEmpty();
    }
}