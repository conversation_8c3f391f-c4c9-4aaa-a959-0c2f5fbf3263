package com.ebon.energy.fms.common.constants;

/**
 * UI字符串常量类
 * 定义用户界面中使用的各种字符串常量
 */
public class UIStrings {

    public static final String TEXT_FOR_NO_SYSTEM_STATUS = "No device data";

    public static final String DOC_REDBACK_TS_AND_CS = "RedbackTechnologies_Terms-and-Conditions.pdf";
    public static final String DOC_REDBACK_PRIVACY_POLICY = "Redback_PrivacyPolicy.pdf";
    public static final String URL_REDBACK_TS_AND_CS = "https://redbacktech.com/terms-conditions/";
    public static final String URL_REDBACK_PRIVACY_POLICY = "https://redbacktech.com/privacy-policy/";

    public static final String UNAUTHORISED_TO_RESET_PASSWORD = "Unauthorised to reset password";
    public static final String UNAUTHORISED_TO_RESEND_ACTIVATION_EMAIL = "Unauthorised to send activation email";
    public static final String UNAUTHORISED_TO_UNLOCK_ACCOUNT = "Unauthorised to unlock the account";

    public static final String NOTIFY_ERROR_MODELSTATE_KEY = "GlobalModelStateErrorKey";
    public static final String NOTIFY_SUCCESS_MODELSTATE_KEY = "GlobalModelStateSuccessKey";

    public static final String UNEXPECTED_EXCEPTION = "Server Error, please try again.";

    public static final String MISSING_METER_IDENTIFIER = "Meter identifier number is required";

    /**
     * 私有构造函数，防止实例化
     */
    private UIStrings() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
