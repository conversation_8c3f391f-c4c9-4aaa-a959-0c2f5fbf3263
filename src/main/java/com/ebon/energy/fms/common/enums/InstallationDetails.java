package com.ebon.energy.fms.common.enums;

import lombok.Getter;

/**
 * 安装详情枚举
 * 定义安装过程中的各种详细信息类型
 */
@Getter
public enum InstallationDetails {
    SolarPanelManufacturer("SolarPanelManufacturer", "太阳能板制造商"),
    SolarPanelType("SolarPanelType", "太阳能板类型"),
    IsOffgrid("IsOffgrid", "是否离网"),
    NumberOfPanels1("NumberOfPanels1", "第一组太阳能板数量"),
    PanelDirection1("PanelDirection1", "第一组太阳能板方向"),
    NumberOfPanels2("NumberOfPanels2", "第二组太阳能板数量"),
    PanelDirection2("PanelDirection2", "第二组太阳能板方向"),
    PVSize1("PVSize1", "第一组太阳能板功率"),
    PVSize2("PVSize2", "第二组太阳能板功率"),
    FirstConnectionTime("FirstConnectionTime", "首次连接时间"),
    ConnectionPointIdentifier("ConnectionPointIdentifier", "连接点标识符"), // for example, NMI/ICP
    ConnectionPointIdentifierOptOut("ConnectionPointIdentifierOptOut", "连接点标识符退出"),
    SolarPanelConfigurationState("SolarPanelConfigurationState", "太阳能板配置状态"),
    MeterCheckResult("MeterCheckResult", "电表检查结果"),
    InstallerProponentId("InstallerProponentId", "安装商提议者ID"),
    CsipDnspId("CsipDnspId", "CSIP DNSP ID"),
    CsipExportType("CsipExportType", "CSIP导出类型"),
    NumberOfPanels3("NumberOfPanels3", "第三组太阳能板数量"),
    PanelDirection3("PanelDirection3", "第三组太阳能板方向"),
    PVSize3("PVSize3", "第三组太阳能板功率");

    private final String key;
    private final String description;

    InstallationDetails(String key, String description) {
        this.key = key;
        this.description = description;
    }
}
