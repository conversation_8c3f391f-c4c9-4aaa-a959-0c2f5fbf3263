// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.common.enums;

import lombok.Getter;

/**
 * Australia states or territories
 */
@Getter
public enum AustralianState {
    /**
     * Not Australia
     */
    Unknown(0, "Unknown"),

    /**
     * ACT
     */
    ACT(1, "Australian Capital Territory"),

    /**
     * ACT
     */
    AustralianCapitalTerritory(1, "Australian Capital Territory"),

    /**
     * NSW
     */
    NSW(2, "New South Wales"),

    /**
     * NSW
     */
    NewSouthWales(2, "New South Wales"),

    /**
     * QLD
     */
    QLD(3, "Queensland"),

    /**
     * QLD
     */
    Queensland(3, "Queensland"),

    /**
     * SA
     */
    SA(4, "South Australia"),

    /**
     * SA
     */
    SouthAustralia(4, "South Australia"),

    /**
     * TAS
     */
    TAS(5, "Tasmania"),

    /**
     * TAS
     */
    Tasmania(5, "Tasmania"),

    /**
     * VIC
     */
    VIC(6, "Victoria"),

    /**
     * vic
     */
    Victoria(6, "Victoria"),

    /**
     * wa
     */
    WA(7, "Western Australia"),

    /**
     * wa
     */
    WesternAustralia(7, "Western Australia"),

    /**
     * NT
     */
    NT(8, "Northern Territory"),

    /**
     * NT
     */
    NorthernTerritory(8, "Northern Territory");

    private final int value;
    private final String description;

    AustralianState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 根据代码或全名查找州
     * @param input 州代码或全名
     * @return 对应的州枚举，如果未找到则返回null
     */
    public static AustralianState fromString(String input) {
        if (input == null || input.trim().isEmpty()) {
            return Unknown;
        }

        String trimmedValue = input.trim();
        for (AustralianState state : values()) {
            if (state.name().equalsIgnoreCase(trimmedValue) ||
                state.description.equalsIgnoreCase(trimmedValue)) {
                return state;
            }
        }
        return Unknown;
    }
}
