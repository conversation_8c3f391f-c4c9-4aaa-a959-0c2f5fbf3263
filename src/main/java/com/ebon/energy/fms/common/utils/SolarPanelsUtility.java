package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.PanelDirection;
import com.ebon.energy.fms.common.enums.SolarPanelManufacturer;

import java.util.Optional;

/**
 * 太阳能板工具类
 * 提供太阳能板相关的解析和处理方法
 */
public class SolarPanelsUtility {

    /**
     * 解析太阳能板方向字符串
     * @param panelDirection 方向字符串
     * @return 对应的PanelDirection枚举，如果输入为空则返回null
     */
    public static PanelDirection parseDirection(String panelDirection) {
        if (panelDirection == null || panelDirection.trim().isEmpty()) {
            return null;
        }

        try {
            return PanelDirection.valueOf(panelDirection.trim());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 解析太阳能板制造商字符串
     * @param manufacturer 制造商字符串
     * @return 对应的SolarPanelManufacturer枚举，如果未找到则返回None
     */
    public static SolarPanelManufacturer parseSolarPanelManufacturer(String manufacturer) {
        if (manufacturer == null || manufacturer.trim().isEmpty()) {
            return SolarPanelManufacturer.None;
        }

        try {
            return SolarPanelManufacturer.valueOf(manufacturer.trim());
        } catch (IllegalArgumentException e) {
            return SolarPanelManufacturer.None;
        }
    }

    /**
     * 解析字符串为整数
     * @param value 字符串值
     * @return 解析成功返回整数，否则返回null
     */
    public static Integer parseToInt(String value) {
        return Optional.ofNullable(value)
                .filter(v -> !v.trim().isEmpty())
                .map(String::trim)
                .map(v -> {
                    try {
                        return Integer.parseInt(v);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .orElse(null);
    }

    /**
     * 解析字符串为双精度浮点数
     * @param value 字符串值
     * @return 解析成功返回双精度浮点数，否则返回null
     */
    public static Double parseToDouble(String value) {
        return Optional.ofNullable(value)
                .filter(v -> !v.trim().isEmpty())
                .map(String::trim)
                .map(v -> {
                    try {
                        return Double.parseDouble(v);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .orElse(null);
    }

    /**
     * 解析字符串为布尔值
     * @param value 字符串值
     * @return 解析成功返回布尔值，否则返回false
     */
    public static boolean parseToBool(String value) {
        return Optional.ofNullable(value)
                .map(String::trim)
                .map(v -> "true".equalsIgnoreCase(v) || "1".equals(v) || "yes".equalsIgnoreCase(v))
                .orElse(false);
    }
}
