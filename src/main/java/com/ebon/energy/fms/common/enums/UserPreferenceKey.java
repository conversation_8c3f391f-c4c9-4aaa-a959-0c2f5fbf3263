package com.ebon.energy.fms.common.enums;

import lombok.Getter;

/**
 * 用户偏好设置键枚举
 * 定义用户可以设置的各种偏好选项
 */
@Getter
public enum UserPreferenceKey {
    ProductFriendlyName("ProductFriendlyName", "产品友好名称"),
    DefaultDashboard("DefaultDashboard", "默认仪表板"),
    TimeZonePreference("TimeZonePreference", "时区偏好"),
    LanguagePreference("LanguagePreference", "语言偏好"),
    NotificationSettings("NotificationSettings", "通知设置");

    private final String key;
    private final String description;

    UserPreferenceKey(String key, String description) {
        this.key = key;
        this.description = description;
    }
}
