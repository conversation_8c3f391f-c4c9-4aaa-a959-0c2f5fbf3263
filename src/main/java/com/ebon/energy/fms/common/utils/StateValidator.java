package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.AustralianState;

/**
 * 州验证器工具类
 * 用于验证和解析澳大利亚州信息
 */
public class StateValidator {

    /**
     * 尝试解析州字符串为AustralianState枚举
     * @param stateString 州字符串
     * @param state 输出参数，解析成功时包含对应的州枚举
     * @return 如果解析成功返回true，否则返回false
     */
    public static boolean tryParse(String stateString, AustralianState[] state) {
        AustralianState parsedState = AustralianState.fromString(stateString);
        if (parsedState != null) {
            state[0] = parsedState;
            return true;
        }
        return false;
    }

    /**
     * 尝试解析州字符串为AustralianState枚举（简化版本）
     * @param stateString 州字符串
     * @return 解析成功返回对应的州枚举，否则返回null
     */
    public static AustralianState tryParse(String stateString) {
        return AustralianState.fromString(stateString);
    }

    /**
     * 验证州字符串是否有效
     * @param stateString 州字符串
     * @return 如果是有效的澳大利亚州返回true，否则返回false
     */
    public static boolean isValidState(String stateString) {
        return AustralianState.fromString(stateString) != null;
    }
}
