package com.ebon.energy.fms.domain.vo.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * NMI验证结果
 * 包含NMI验证的详细结果信息
 */
@Data
public class NMIValidationResult {

    @JsonProperty("IsValid")
    private boolean isValid;

    @JsonProperty("IsMandatory")
    private boolean isMandatory;

    @JsonProperty("LastDateNMIMandatoryRequired")
    private LocalDateTime lastDateNMIMandatoryRequired;

    @JsonProperty("ErrorMessage")
    private String errorMessage;

    public NMIValidationResult(boolean isValid, boolean isMandatory, LocalDateTime lastDateNMIMandatoryRequired) {
        this.isValid = isValid;
        this.isMandatory = isMandatory;
        this.lastDateNMIMandatoryRequired = lastDateNMIMandatoryRequired;
    }

    public NMIValidationResult(boolean isValid, boolean isMandatory, LocalDateTime lastDateNMIMandatoryRequired, String errorMessage) {
        this.isValid = isValid;
        this.isMandatory = isMandatory;
        this.lastDateNMIMandatoryRequired = lastDateNMIMandatoryRequired;
        this.errorMessage = errorMessage;
    }
}
