// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.utils.IRedbackAddress;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地址数据传输对象
 * 实现IRedbackAddress接口，用于地址信息的传输和处理
 */
@Data
@NoArgsConstructor
public class AddressDTO implements IRedbackAddress {

    @JsonProperty("GooglePlaceId")
    private String googlePlaceId;

    @JsonProperty("FullAddress")
    private String fullAddress;

    @JsonProperty("AddressLineOne")
    private String addressLineOne;

    @JsonProperty("AddressLineTwo")
    private String addressLineTwo;

    @JsonProperty("Suburb")
    private String suburb;

    @JsonProperty("State")
    private String state;

    @JsonProperty("Country")
    private String country;

    @JsonProperty("PostCode")
    private String postCode;

    @JsonProperty("Latitude")
    private String latitude;

    @JsonProperty("Longitude")
    private String longitude;

    @JsonProperty("TimeZoneId")
    private String timeZoneId;

    public AddressDTO(String googlePlaceId, String fullAddress, String addressLineOne, 
                     String addressLineTwo, String suburb, String state, String country, 
                     String postCode, String latitude, String longitude) {
        this.googlePlaceId = googlePlaceId;
        this.fullAddress = fullAddress;
        this.addressLineOne = addressLineOne;
        this.addressLineTwo = addressLineTwo;
        this.suburb = suburb;
        this.state = state;
        this.country = country;
        this.postCode = postCode;
        this.latitude = latitude;
        this.longitude = longitude;
    }

    /**
     * 从IRedbackAddress创建AddressDTO实例
     * @param address IRedbackAddress实例
     * @return AddressDTO实例，如果输入为null则返回null
     */
    @JsonProperty("FromAddress")
    public static AddressDTO fromAddress(IRedbackAddress address) {
        if (address == null) {
            return null;
        }

        return new AddressDTO(
                address.getGooglePlaceId(),
                null, // fullAddress在原C#代码中设为null
                address.getAddressLineOne(),
                address.getAddressLineTwo(),
                address.getSuburb(),
                address.getState(),
                address.getCountry(),
                address.getPostCode(),
                address.getLatitude(),
                address.getLongitude()
        );
    }
}
