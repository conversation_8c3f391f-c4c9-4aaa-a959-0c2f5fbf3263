package com.ebon.energy.fms.domain.vo.detail;

import com.ebon.energy.fms.common.enums.SolarPanelManufacturer;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class InstallationDetailsViewModel {

    @JsonProperty("SerialNumber")
    private String serialNumber;

    @JsonProperty("MustHaveGrid")
    private boolean mustHaveGrid;

    @JsonProperty("IsOnGrid")
    private boolean isOnGrid;

    @JsonProperty("InstalledProductSupportsConnectedPV")
    private boolean installedProductSupportsConnectedPV;

    @JsonProperty("SolarPanelManufacturer")
    private SolarPanelManufacturer solarPanelManufacturer; // 可空 -> 引用类型即

    @JsonProperty("SolarPanelType")
    @Length(max = 50,message = "Solar panel type must be less than 50 characters")
    private String solarPanelType;

    @JsonProperty("SolarPanelType1")
    private String solarPanelType1;

    @JsonProperty("SolarPanels")
    private List<SolarPanelViewModel> solarPanels;

    @JsonProperty("Address")
    private InstallationAddressViewModel address;

    @JsonProperty("ConnectionPointIdentifier")
    private String connectionPointIdentifier;

    @JsonProperty("IsNMIOptOut")
    private boolean isNMIOptOut;

    @JsonProperty("IsNMIValid")
    private Boolean isNMIValid; // C# bool? -> Java Boolean

    @JsonProperty("IsNMIMandatory")
    private boolean isNMIMandatory;

    @JsonProperty("LastDateNMIMandatoryRequired")
    private LocalDateTime lastDateNMIMandatoryRequired;

    @JsonProperty("HasEditOnInstallationDetails")
    private boolean hasEditOnInstallationDetails;

    @JsonProperty("Photos")
    private List<InstallationPhotoDescriptorDto> photos; // IEnumerable -> List

    @JsonProperty("FriendlyName")
    @Length(max = 50,message = "Friendly name must be less than 50 characters")
    private String friendlyName;

    @JsonProperty("InstallationStartDate")
    private LocalDateTime installationStartDate;

    @JsonProperty("CsipLfdi")
    private String csipLfdi; // 默认即为 null
}