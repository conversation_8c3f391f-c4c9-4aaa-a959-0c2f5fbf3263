package com.ebon.energy.fms.domain.vo.detail;

import com.ebon.energy.fms.common.enums.PanelDirection;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 太阳能板数据传输对象
 * 用于太阳能板信息的传输
 */
@Data
public class SolarPanel {

    @JsonProperty("PanelDirection")
    private PanelDirection panelDirection;

    @JsonProperty("NumberOfPanels")
    private int numberOfPanels;

    @JsonProperty("PVSize")
    private double pvSize;
}
