package com.ebon.energy.fms.domain.vo.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户偏好设置视图模型
 * 用于用户偏好设置的展示和传输
 */
@Data
public class PreferencesViewModel {

    @JsonProperty("SerialNumber")
    private String serialNumber;

    @JsonProperty("FriendlyName")
    private String friendlyName;

    @JsonProperty("CanResetPin")
    private Boolean canResetPin;
}
